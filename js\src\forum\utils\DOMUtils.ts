/**
 * DOM manipulation utilities
 */
export class DOMUtils {
    /**
     * Create a DOM element with specified attributes
     * @param {string} tagName - HTML tag name
     * @param {object} attributes - Element attributes
     * @param {string} innerHTML - Inner HTML content
     * @returns {HTMLElement} Created element
     */
    static createElement(
        tagName: string,
        attributes: Record<string, string> = {},
        innerHTML: string = ''
    ): HTMLElement {
        const element = document.createElement(tagName);

        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'style') {
                element.setAttribute('style', value);
            } else {
                element.setAttribute(key, value);
            }
        });

        if (innerHTML) {
            element.innerHTML = innerHTML;
        }

        return element;
    }

    /**
     * Safely get element by ID
     * @param {string} id - Element ID
     * @returns {HTMLElement | null} Element or null if not found
     */
    static getElementById(id: string): HTMLElement | null {
        return document.getElementById(id);
    }

    /**
     * Safely query selector
     * @param {string} selector - CSS selector
     * @param {Element} parent - Parent element (default: document)
     * @returns {Element | null} Element or null if not found
     */
    static querySelector(selector: string, parent: Element | Document = document): Element | null {
        try {
            if (!parent || !selector) {
                return null;
            }
            return parent.querySelector(selector);
        } catch {
            return null;
        }
    }

    /**
     * Safely query all elements
     * @param {string} selector - CSS selector
     * @param {Element} parent - Parent element (default: document)
     * @returns {NodeListOf<Element>} NodeList of elements
     */
    static querySelectorAll(selector: string, parent: Element | Document = document): NodeListOf<Element> {
        try {
            if (!parent || !selector) {
                return document.querySelectorAll(''); // Return empty NodeList
            }
            return parent.querySelectorAll(selector);
        } catch {
            return document.querySelectorAll(''); // Return empty NodeList
        }
    }

    /**
     * Add event listener with error handling
     * @param {Element} element - Target element
     * @param {string} event - Event type
     * @param {Function} handler - Event handler
     * @param {boolean} useCapture - Use capture phase
     */
    static addEventListener(
        element: Element,
        event: string,
        handler: EventListener,
        useCapture: boolean = false
    ): void {
        try {
            if (element && event && handler) {
                element.addEventListener(event, handler, useCapture);
            }
        } catch {
            // Silently handle event listener errors
        }
    }

    /**
     * Remove event listener with error handling
     * @param {Element} element - Target element
     * @param {string} event - Event type
     * @param {Function} handler - Event handler
     * @param {boolean} useCapture - Use capture phase
     */
    static removeEventListener(
        element: Element,
        event: string,
        handler: EventListener,
        useCapture: boolean = false
    ): void {
        try {
            if (element && event && handler) {
                element.removeEventListener(event, handler, useCapture);
            }
        } catch {
            // Silently handle event listener removal errors
        }
    }

    /**
     * Set CSS styles on element
     * @param {HTMLElement} element - Target element
     * @param {object} styles - Style properties
     */
    static setStyles(element: HTMLElement, styles: Record<string, string>): void {
        if (!element || !styles) {
            return;
        }
        Object.entries(styles).forEach(([property, value]) => {
            try {
                element.style.setProperty(property, value);
            } catch {
                // Silently handle style setting errors
            }
        });
    }

    /**
     * Append element to parent with error handling
     * @param {Element} parent - Parent element
     * @param {Element} child - Child element to append
     */
    static appendChild(parent: Element, child: Element): void {
        try {
            if (parent && child) {
                parent.appendChild(child);
            }
        } catch {
            // Silently handle append errors
        }
    }

    /**
     * Prepend element to parent with error handling
     * @param {Element} parent - Parent element
     * @param {Element} child - Child element to prepend
     */
    static prependChild(parent: Element, child: Element): void {
        try {
            if (parent && child) {
                parent.insertBefore(child, parent.firstChild);
            }
        } catch {
            // Silently handle prepend errors
        }
    }

    /**
     * Remove element safely
     * @param {Element} element - Element to remove
     */
    static removeElement(element: Element): void {
        try {
            if (element && element.parentNode) {
                element.parentNode.removeChild(element);
            }
        } catch {
            // Silently handle element removal errors
        }
    }
}
